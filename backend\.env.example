# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_NAME=loan_app

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h

# Face API Configuration
FACE_API_MODELS_PATH=./models

# Mobile Money Integration
MOBILE_MONEY_API_KEY=your_api_key
MOBILE_MONEY_API_SECRET=your_api_secret
MOBILE_MONEY_WEBHOOK_SECRET=your_webhook_secret

# File Upload Configuration
MAX_FILE_SIZE=5242880 # 5MB
UPLOAD_DIR=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# MTN Mobile Money Sandbox Configuration
MTN_BASE_URL=https://sandbox.momodeveloper.mtn.com/
MTN_COLLECTIONS_SUBSCRIPTION_KEY=ac1778b3a8ec4ebaa611fc9ddaab207d
MTN_DISBURSEMENT_SUBSCRIPTION_KEY=f32fec3e85f64460af1b5e4eaa5d9b77
MTN_API_USER=2e2f0f01-16c7-4a2e-b35b-ba3f921baef9
MTN_API_KEY=6e4f6b422e094b42a150fd4133336f23
MTN_TARGET_ENVIRONMENT=sandbox
MTN_CALLBACK_HOST=webhook.site
MTN_CALLBACK_URL=https://webhook.site/mywebhooksandbox

# Feature Flags
USE_MTN_SANDBOX=true