const { LoanService } = require('../src/services/loan.service');
const { TransactionService } = require('../src/services/transaction.service');

(async () => {
  const loanService = new LoanService();
  const transactionService = new TransactionService();

  try {
    console.log('Testing loan disbursement...');
    const disbursementResult = await loanService.disburseLoanWithMTN('test-loan-id', 100, '256123456789');
    console.log('Disbursement Result:', disbursementResult);

    console.log('Testing loan repayment...');
    const repaymentResult = await loanService.repayLoanWithMTN('test-transaction-id', 50, '256123456789');
    console.log('Repayment Result:', repaymentResult);

    console.log('Testing transaction status update...');
    await transactionService.updateTransactionStatus('test-transaction-id', 'SUCCESS');
    console.log('Transaction status updated successfully.');

  } catch (error) {
    console.error('Error during testing:', error);
  }
})();
