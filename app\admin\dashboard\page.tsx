"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"

import { RecentTransactionsCard } from "@/components/recent-transactions-card"
import { LoanStatsCard } from "@/components/loan-stats-card"
import { UserStatsCard } from "@/components/user-stats-card"
import { OverviewChart } from "@/components/overview-chart"
import { BalanceCard } from "@/components/balance-card"
import { adminApi } from "@/lib/admin-api"
import { toast } from "sonner"

interface DashboardData {
  userStats: {
    totalUsers: number
    activeUsers: number
    newUsersThisMonth: number
    verifiedUsers: number
    pendingUsers: number
    userGrowthRate: number
  }
  loanStats: {
    totalLoans: number
    activeLoans: number
    pendingLoans: number
    approvedLoans: number
    disbursedLoans: number
    paidLoans: number
    overdueLoans: number
    totalLoanAmount: number
    totalOutstandingAmount: number
    totalPaidAmount: number
    averageLoanAmount: number
    approvalRate: number
    defaultRate: number
  }
  transactionStats: {
    totalTransactions: number
    completedTransactions: number
    pendingTransactions: number
    failedTransactions: number
    totalTransactionVolume: number
    totalDeposits: number
    totalWithdrawals: number
    totalDisbursements: number
    totalRepayments: number
  }
  financialStats: {
    totalRevenue: number
    monthlyRevenue: number
    totalInterestEarned: number
    cashFlow: number
    profitMargin: number
  }
  systemStats: {
    totalDocuments: number
    pendingDocuments: number
    approvedDocuments: number
    rejectedDocuments: number
    systemUptime: number
  }
}

interface Transaction {
  id: string
  type: "deposit" | "withdrawal" | "loan_disbursement" | "loan_repayment"
  amount: number
  createdAt: string
  description: string
  status: "completed" | "pending" | "failed"
  userName: string
}

export default function AdminDashboardPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [recentTransactions, setRecentTransactions] = useState<Transaction[]>([])
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      console.log('Fetching dashboard data...')

      // Fetch dashboard overview which includes both stats and recent transactions
      const response = await adminApi.getDashboardOverview()

      console.log('Dashboard overview response:', response)

      if (response.success) {
        console.log('Dashboard data received:', response.data)
        setDashboardData(response.data.statistics)
        setRecentTransactions(response.data.recentTransactions || [])
        console.log('User stats from dashboard:', response.data.statistics?.userStats)
      } else {
        throw new Error(response.message || 'Failed to fetch dashboard data')
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch dashboard data')
      toast.error('Failed to load dashboard data')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
        <div className="text-sm text-muted-foreground">Last updated: {new Date().toLocaleString()}</div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          {/*<TabsTrigger value="reports">Reports</TabsTrigger>*/}
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {error && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="pt-6">
                <div className="flex items-center space-x-2">
                  <div className="text-red-600">⚠️</div>
                  <div>
                    <p className="text-sm font-medium text-red-800">Error loading dashboard data</p>
                    <p className="text-xs text-red-600">{error}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {/* Mobile Money Balance Card - Now integrated with MTN API */}
            <BalanceCard isLoading={isLoading} />

            <LoanStatsCard
              isLoading={isLoading}
              data={dashboardData?.loanStats ? {
                activeLoans: dashboardData.loanStats.activeLoans,
                totalLoanAmount: dashboardData.loanStats.totalLoanAmount,
                approvalRate: dashboardData.loanStats.approvalRate,
                totalOutstandingAmount: dashboardData.loanStats.totalOutstandingAmount
              } : undefined}
            />

            <UserStatsCard
              isLoading={isLoading}
              data={dashboardData?.userStats ? {
                totalUsers: dashboardData.userStats.totalUsers,
                newUsersThisMonth: dashboardData.userStats.newUsersThisMonth,
                userGrowthRate: dashboardData.userStats.userGrowthRate,
                activeUsers: dashboardData.userStats.activeUsers
              } : undefined}
            />

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">System Status</CardTitle>
                <CardDescription>All systems operational</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-500">
                  {dashboardData?.systemStats?.systemUptime?.toFixed(1) || '99.9'}%
                </div>
                <p className="text-xs text-muted-foreground">Uptime in the last 30 days</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
            <OverviewChart className="lg:col-span-4" isLoading={isLoading} />
            <RecentTransactionsCard
              className="lg:col-span-3"
              isLoading={isLoading}
              data={recentTransactions}
            />
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Analytics</CardTitle>
              <CardDescription>Detailed analytics and insights will be displayed here.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center border rounded-md">
                <p className="text-muted-foreground">Analytics dashboard coming soon</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>


        <TabsContent value="reports">
          <Card>
            <CardHeader>
              <CardTitle>Reports</CardTitle>
              <CardDescription>Generate and view reports for your loan business.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center border rounded-md">
                <p className="text-muted-foreground">Reports dashboard coming soon</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

      </Tabs>
    </div>
  )
}

