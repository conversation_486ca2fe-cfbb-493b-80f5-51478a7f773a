# Umlamleli Loan Management System - Implementation Log

## Table of Contents
- [Introduction](#introduction)
- [Feature Implementations](#feature-implementations)
  - [Advanced Filtering and Sorting](#advanced-filtering-and-sorting)
  - [Date Format Correction](#date-format-correction)
  - [Mobile Responsiveness Improvements](#mobile-responsiveness-improvements)
- [Bug Fixes](#bug-fixes)
  - [PostgreSQL Enum Type Error](#postgresql-enum-type-error)
  - [Loan Status Display Issue](#loan-status-display-issue)
- [Challenges and Solutions](#challenges-and-solutions)
- [Future Recommendations](#future-recommendations)

## Introduction

This document provides a chronological record of feature implementations and bug fixes for the Umlamleli Loan Management System. It includes details of code changes, challenges encountered, and solutions implemented during the development process.

### Project Overview

The Umlamleli Loan Management System is a web application designed to facilitate microloans in Eswatini. The system allows users to:

- Apply for loans with various terms and purposes
- Track loan status and repayment schedules
- Make loan repayments
- View loan history and details
- Manage their profile and account information

The application uses a modern tech stack:
- **Frontend**: Next.js with TypeScript, Tailwind CSS, and shadcn/ui components
- **Backend**: Node.js with Express and TypeScript
- **Database**: PostgreSQL with TypeORM
- **Authentication**: JWT-based authentication
- **Face Recognition**: face-api.js for identity verification

### Development Timeline

The development process has been iterative, with features and bug fixes implemented based on user feedback and testing. The key milestones include:

1. **Initial Setup** - Basic loan application and management functionality
2. **User Authentication** - Implementation of secure login with face recognition
3. **Loan Management** - Core loan application, approval, and repayment workflows
4. **Admin Dashboard** - Administrative tools for loan approval and user management
5. **UI Enhancements** - Improved mobile responsiveness and user experience
6. **Advanced Features** - Filtering, sorting, and data visualization tools

This document focuses on the most recent implementations and bug fixes, particularly those related to the loan history page and its associated functionality.

## Feature Implementations

### Advanced Filtering and Sorting

**Date Implemented**: May 2023

**Description**: Added comprehensive filtering options to allow users to find specific loans based on multiple criteria, including status, date ranges, and amount ranges. Implemented sorting functionality for all columns and added localStorage persistence for filter preferences.

**Files Modified**:
- `lib/api.ts` - Updated API client to support filtered queries
- `backend/src/controllers/loan.controller.ts` - Enhanced controller to handle filter parameters
- `backend/src/services/loan.service.ts` - Added filtered query support
- `backend/src/routes/loan.routes.ts` - Updated routes
- `app/loan/history/page.tsx` - Updated UI to include filters and sorting
- `components/loan/LoanFilters.tsx` - Created new component for filters
- `lib/constants.ts` - Added loan status constants

**Key Changes**:

1. Created a reusable `LoanFilters` component:

```tsx
// components/loan/LoanFilters.tsx
export function LoanFilters({ onFilterChange, className }: LoanFiltersProps) {
  const [filters, setFilters] = useState<LoanFilters>({});
  const [searchInput, setSearchInput] = useState("");
  const [minAmountInput, setMinAmountInput] = useState("");
  const [maxAmountInput, setMaxAmountInput] = useState("");
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  // Load filters from localStorage on component mount
  useEffect(() => {
    const savedFilters = localStorage.getItem(STORAGE_KEY);
    if (savedFilters) {
      try {
        const parsedFilters = JSON.parse(savedFilters);
        // Apply filters
        setFilters(parsedFilters);
        onFilterChange(parsedFilters);
      } catch (error) {
        console.error("Error parsing saved filters:", error);
      }
    }
  }, [onFilterChange]);

  // Save filters to localStorage whenever they change
  useEffect(() => {
    if (Object.keys(filters).length > 0) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(filters));
    }
  }, [filters]);

  // Filter handling methods...
}
```

2. Enhanced the backend service to support filtered queries:

```typescript
// backend/src/services/loan.service.ts
async getUserLoansFiltered(userId: string, filters: any = {}): Promise<Loan[]> {
  // Find user
  const user = await userRepository.findOne({ where: { id: userId } });
  if (!user) {
    throw new Error('User not found');
  }

  // Build query conditions
  const queryBuilder = loanRepository.createQueryBuilder('loan')
    .leftJoinAndSelect('loan.user', 'user')
    .where('user.id = :userId', { userId });

  // Apply filters
  if (filters.status) {
    // For enum types, we need to use exact matching without LOWER() function
    queryBuilder.andWhere('loan.status = :status', { status: filters.status });
  }

  if (filters.minAmount) {
    queryBuilder.andWhere('loan.amount >= :minAmount', { minAmount: filters.minAmount });
  }

  // Additional filter conditions...

  // Order by creation date (newest first)
  queryBuilder.orderBy('loan.createdAt', 'DESC');

  // Execute query
  return queryBuilder.getMany();
}
```

3. Added sorting functionality to the loan history page:

```tsx
// app/loan/history/page.tsx
const handleSort = (field: string) => {
  const newDirection = field === sortField && sortDirection === "asc" ? "desc" : "asc";
  setSortField(field);
  setSortDirection(newDirection);

  // Sort the loans array
  const sortedLoans = [...loans].sort((a, b) => {
    let aValue = a[field as keyof Loan];
    let bValue = b[field as keyof Loan];

    // Handle date fields
    if (field === "createdAt" || field === "dueDate") {
      aValue = new Date(aValue as string).getTime();
      bValue = new Date(bValue as string).getTime();
    }

    // Handle numeric fields
    if (typeof aValue === "number" && typeof bValue === "number") {
      return newDirection === "asc" ? aValue - bValue : bValue - aValue;
    }

    // Handle string fields
    if (typeof aValue === "string" && typeof bValue === "string") {
      return newDirection === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    return 0;
  });

  setLoans(sortedLoans);
};
```

**Challenges**:
- Ensuring proper handling of enum types in PostgreSQL queries
- Implementing responsive design for filters on mobile devices
- Maintaining filter state between sessions

**Results**: Users can now easily filter and sort their loan history, making it much easier to find specific loans and analyze their borrowing patterns.

### Date Format Correction

**Date Implemented**: May 2023

**Description**: Fixed the date formatting to ensure consistent "DD/MM/YYYY" format for all dates displayed in the loan history page.

**Files Modified**:
- `app/loan/history/page.tsx` - Updated the formatDate function

**Key Changes**:

```tsx
// app/loan/history/page.tsx
const formatDate = (dateString: string) => {
  try {
    // Parse the date
    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "Invalid date";
    }

    // Format as DD/MM/YYYY
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  } catch (e) {
    return "Invalid date";
  }
}
```

**Results**: All dates in the loan history page are now consistently displayed in the "DD/MM/YYYY" format, improving readability and user experience.

### Mobile Responsiveness Improvements

**Date Implemented**: May 2023

**Description**: Enhanced the mobile responsiveness of the loan history page to ensure all information is accessible on smaller screens.

**Files Modified**:
- `app/loan/history/page.tsx` - Updated table layout and added expandable rows

**Key Changes**:

1. Added horizontal scrolling for the table on mobile devices:

```tsx
<div className="overflow-x-auto -mx-4 sm:mx-0">
  <Table className="min-w-[800px] sm:min-w-full">
    {/* Table content */}
  </Table>
</div>
```

2. Implemented expandable rows for better mobile viewing:

```tsx
{expandedRowId === loan.id && (
  <TableRow className="bg-blue-50">
    <TableCell colSpan={10} className="p-4">
      <div className="grid grid-cols-2 gap-4 text-sm">
        {/* Expanded row content with all loan details */}
      </div>
    </TableCell>
  </TableRow>
)}
```

**Results**: The loan history page now provides a much better experience on mobile devices, with all information accessible either directly in the table or through the expandable row feature.

## Bug Fixes

### PostgreSQL Enum Type Error

**Date Fixed**: May 2023

**Description**: Fixed an error that occurred when trying to apply the `LOWER()` function to an enum type in PostgreSQL queries.

**Error Message**: `Error: function lower(loans_status_enum) does not exist`

**Files Modified**:
- `backend/src/services/loan.service.ts` - Updated query handling for enum types

**Key Changes**:

```typescript
// Before:
if (filters.status) {
  queryBuilder.andWhere('LOWER(loan.status) = LOWER(:status)', { status: filters.status });
}

// After:
if (filters.status) {
  // For enum types, we need to use exact matching without LOWER() function
  queryBuilder.andWhere('loan.status = :status', { status: filters.status });
}
```

**Root Cause**: PostgreSQL doesn't support applying string functions like `LOWER()` directly to enum types.

**Solution**: Used exact matching for enum types instead of case-insensitive matching with the `LOWER()` function.

### Loan Status Display Issue

**Date Fixed**: May 2023

**Description**: Fixed an issue where loans with the status "approved" were incorrectly being displayed as "Active" in the UI.

**Files Modified**:
- `app/loan/history/page.tsx` - Updated the getStatusBadge function

**Key Changes**:

```tsx
// Before:
const getStatusBadge = (status: string) => {
  switch (status.toLowerCase()) {
    case "active":
    case "approved":
      return <Badge className="bg-blue-500">Active</Badge>
    // Other cases...
  }
}

// After:
const getStatusBadge = (status: string) => {
  switch (status.toLowerCase()) {
    case "approved":
      return <Badge className="bg-green-500">Approved</Badge>
    case "disbursed":
      return <Badge className="bg-blue-500">Disbursed</Badge>
    // Other cases...
  }
}
```

**Root Cause**: The `getStatusBadge` function was incorrectly mapping the "approved" status to display as "Active".

**Solution**: Updated the function to correctly display "Approved" for loans with the "approved" status and used appropriate colors for each status.

## Challenges and Solutions

### 1. PostgreSQL Enum Type Handling

**Challenge**: PostgreSQL has specific requirements for handling enum types in queries, which caused errors when using string functions like `LOWER()`. This resulted in the error: `Error: function lower(loans_status_enum) does not exist` when trying to filter loans by status.

**Solution**:
- Modified the query building logic to use exact matching for enum types instead of case-insensitive matching
- Implemented proper error handling for enum type comparisons
- Added validation in the controller to ensure only valid enum values are used in queries
- Updated the search functionality to handle enum types properly when searching for loans

```typescript
// Modified query for status filtering
if (filters.status) {
  // For enum types, we need to use exact matching without LOWER() function
  queryBuilder.andWhere('loan.status = :status', { status: filters.status });
}

// Enhanced search functionality for enum types
if (filters.search) {
  // Get matching statuses if any
  const matchingStatuses = Object.values(LoanStatus).filter(status =>
    status.toLowerCase().includes(filters.search.toLowerCase())
  );

  // Build the search condition
  const searchConditions = [
    'loan.purpose ILIKE :search',
    'CAST(loan.id as TEXT) ILIKE :search'
  ];

  // Add status condition if we have matching statuses
  if (matchingStatuses.length > 0) {
    searchConditions.push('loan.status IN (:...statuses)');
  }

  // Combine all conditions with OR, but keep them within parentheses
  queryBuilder.andWhere(`(${searchConditions.join(' OR ')})`, {
    search: `%${filters.search}%`,
    statuses: matchingStatuses
  });
}
```

### 2. Mobile Responsiveness

**Challenge**: The loan history table contained many columns (Loan ID, Principal, Interest Rate, Total Repayment, Purpose, Collateral, Application Date, Due Date, Status, Actions), making it difficult to display all information on mobile devices, particularly on Android.

**Solution**:
- Implemented a combination of horizontal scrolling and expandable rows
- Set a minimum width for the table to ensure all columns are accessible
- Created a mobile-friendly grid layout for the expanded details
- Added proper event handling to prevent click propagation for action buttons
- Used negative margins to allow the table to extend to the edges of the screen on mobile

```tsx
// Horizontal scrolling container
<div className="overflow-x-auto -mx-4 sm:mx-0">
  <Table className="min-w-[800px] sm:min-w-full">
    {/* Table content */}
  </Table>
</div>

// Expandable row for mobile
{expandedRowId === loan.id && (
  <TableRow className="bg-blue-50">
    <TableCell colSpan={10} className="p-4">
      <div className="grid grid-cols-2 gap-4 text-sm">
        {/* Detailed loan information */}
      </div>
    </TableCell>
  </TableRow>
)}
```

### 3. Filter State Persistence

**Challenge**: Maintaining filter preferences between sessions required a reliable storage mechanism. Users needed their filter settings to persist when they navigated away from the page or closed the browser.

**Solution**:
- Implemented localStorage persistence for filter preferences
- Added proper error handling for parsing saved filters
- Created a clear functionality to reset filters when needed
- Ensured filters are properly restored when the component mounts

```tsx
// Load filters from localStorage on component mount
useEffect(() => {
  const savedFilters = localStorage.getItem(STORAGE_KEY);
  if (savedFilters) {
    try {
      const parsedFilters = JSON.parse(savedFilters);

      // Convert date strings back to Date objects if they exist
      if (parsedFilters.startDate) {
        setStartDate(new Date(parsedFilters.startDate));
      }
      if (parsedFilters.endDate) {
        setEndDate(new Date(parsedFilters.endDate));
      }

      // Set input fields
      setSearchInput(parsedFilters.search || "");
      setMinAmountInput(parsedFilters.minAmount?.toString() || "");
      setMaxAmountInput(parsedFilters.maxAmount?.toString() || "");

      // Apply filters
      setFilters(parsedFilters);
      onFilterChange(parsedFilters);
    } catch (error) {
      console.error("Error parsing saved filters:", error);
      localStorage.removeItem(STORAGE_KEY);
    }
  }
}, [onFilterChange]);

// Save filters to localStorage whenever they change
useEffect(() => {
  if (Object.keys(filters).length > 0) {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(filters));
  }
}, [filters]);

// Clear filters function
const clearFilters = () => {
  setFilters({});
  setSearchInput("");
  setMinAmountInput("");
  setMaxAmountInput("");
  setStartDate(undefined);
  setEndDate(undefined);
  onFilterChange({});
  localStorage.removeItem(STORAGE_KEY);
};
```

### 4. Status Display Consistency

**Challenge**: Loans with the status "approved" were incorrectly being displayed as "Active" in the UI, causing confusion for users. Additionally, the status display needed consistent capitalization and appropriate color coding.

**Solution**:
- Updated the `getStatusBadge` function to correctly display "Approved" for loans with the "approved" status
- Implemented proper capitalization for all status values
- Used appropriate color coding for each status (green for approved/paid, blue for disbursed, yellow for pending, red for rejected/overdue)
- Ensured consistency between the badge display, expanded view, and filter displays

```tsx
// Updated getStatusBadge function
const getStatusBadge = (status: string) => {
  switch (status.toLowerCase()) {
    case "approved":
      return <Badge className="bg-green-500">Approved</Badge>
    case "disbursed":
      return <Badge className="bg-blue-500">Disbursed</Badge>
    case "paid":
    case "completed":
      return <Badge className="bg-green-500">Paid</Badge>
    case "pending":
      return <Badge className="bg-yellow-500">Pending</Badge>
    case "rejected":
      return <Badge className="bg-red-500">Rejected</Badge>
    case "overdue":
      return <Badge className="bg-red-500">Overdue</Badge>
    default:
      return <Badge>{status.charAt(0).toUpperCase() + status.slice(1)}</Badge>
  }
}

## Future Recommendations

### 1. Payment Schedule Timeline

Implement a visual timeline showing past and upcoming payments when a loan row is expanded. This would provide clear visibility into payment history and future obligations, helping users plan their finances.

**Implementation Suggestions**:
- Create a horizontal timeline with payment milestones
- Color-code past (completed), current, and future payments
- Show payment amounts and dates on each milestone
- Integrate with the existing expandable row feature

**Proposed Backend Changes**:
```typescript
// Add to loan.service.ts
async calculatePaymentSchedule(loan: Loan, transactions: Transaction[]): Promise<any[]> {
  const schedule = [];

  // Add disbursement as first item in schedule
  schedule.push({
    date: loan.disbursedAt,
    amount: loan.amount,
    type: 'disbursement',
    status: 'completed'
  });

  // Add all repayments
  const repaymentTransactions = transactions.filter(t =>
    t.type === TransactionType.LOAN_REPAYMENT && t.status === TransactionStatus.COMPLETED
  );

  for (const transaction of repaymentTransactions) {
    schedule.push({
      date: transaction.completedAt || transaction.createdAt,
      amount: transaction.amount,
      type: 'repayment',
      status: 'completed'
    });
  }

  // Calculate remaining amount
  const totalPaid = repaymentTransactions.reduce((sum, t) => sum + t.amount, 0);
  const remainingAmount = loan.amount - totalPaid;

  // If there's still an amount to be paid
  if (remainingAmount > 0 && loan.status !== LoanStatus.PAID) {
    // Add future payment (due date)
    schedule.push({
      date: loan.dueDate,
      amount: remainingAmount,
      type: 'repayment',
      status: new Date() > new Date(loan.dueDate) ? 'overdue' : 'upcoming'
    });
  }

  return schedule.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
}
```

**Proposed Frontend Component**:
```tsx
// components/loan/PaymentTimeline.tsx
export function PaymentTimeline({ loanId }: { loanId: string }) {
  const [schedule, setSchedule] = useState<PaymentEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSchedule = async () => {
      try {
        setIsLoading(true);
        const response = await loanApi.getLoanPaymentSchedule(loanId);
        if (response.success) {
          setSchedule(response.data || []);
        }
      } catch (error) {
        console.error("Error fetching payment schedule:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSchedule();
  }, [loanId]);

  if (isLoading) {
    return <div className="flex justify-center py-4"><Loader2 className="h-6 w-6 animate-spin" /></div>;
  }

  return (
    <div className="mt-4">
      <h4 className="font-medium mb-2">Payment Timeline</h4>
      <div className="relative">
        {/* Timeline line */}
        <div className="absolute top-4 left-0 right-0 h-0.5 bg-gray-200" />

        {/* Timeline events */}
        <div className="relative flex justify-between">
          {schedule.map((event, index) => (
            <div key={index} className="flex flex-col items-center">
              {/* Event dot */}
              <div className={`w-4 h-4 rounded-full z-10 ${
                event.status === 'completed' ? 'bg-green-500' :
                event.status === 'overdue' ? 'bg-red-500' : 'bg-blue-500'
              }`} />

              {/* Event details */}
              <div className="mt-2 text-xs text-center">
                <div>{formatDate(event.date)}</div>
                <div className="font-medium">{formatCurrency(event.amount)}</div>
                <div className="capitalize">{event.type}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
```

### 2. Loan Health Indicators

Add visual indicators showing the "health" of each loan based on payment history and status. This would help users quickly identify problematic loans and encourage timely payments.

**Implementation Suggestions**:
- Create a color-coded health score (0-100%)
- Base score on payment timeliness, amount paid vs. outstanding, and time to due date
- Add small gauge or progress indicators in the table
- Include detailed health information in the expanded view

**Proposed Backend Implementation**:
```typescript
// Add to loan.service.ts
async calculateLoanHealthScore(loanId: string, userId: string): Promise<any> {
  // Find loan
  const loan = await loanRepository.findOne({
    where: { id: loanId, user: { id: userId } }
  });

  if (!loan) {
    throw new Error('Loan not found');
  }

  // Default score components
  let paymentTimelinessScore = 100;
  let amountPaidScore = 0;
  let timeToMaturityScore = 100;

  // Calculate amount paid score (0-100)
  if (loan.amount > 0) {
    amountPaidScore = Math.min(100, Math.round((loan.amountPaid / loan.amount) * 100));
  }

  // Calculate time to maturity score
  if (loan.dueDate && loan.disbursedAt) {
    const now = new Date();
    const dueDate = new Date(loan.dueDate);
    const disbursedDate = new Date(loan.disbursedAt);

    // Total loan duration in days
    const totalDuration = Math.max(1, (dueDate.getTime() - disbursedDate.getTime()) / (1000 * 60 * 60 * 24));

    // Days remaining until due date
    const daysRemaining = Math.max(0, (dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    // If loan is overdue
    if (daysRemaining <= 0) {
      // Penalty increases with days overdue
      const daysOverdue = Math.abs(daysRemaining);
      paymentTimelinessScore = Math.max(0, 100 - Math.min(100, daysOverdue * 5)); // -5 points per day overdue
      timeToMaturityScore = 0;
    } else {
      // Score based on percentage of time remaining
      timeToMaturityScore = Math.round((daysRemaining / totalDuration) * 100);
    }
  }

  // Calculate overall score (weighted average)
  const overallScore = Math.round(
    (paymentTimelinessScore * 0.4) + // 40% weight for payment timeliness
    (amountPaidScore * 0.4) +         // 40% weight for amount paid
    (timeToMaturityScore * 0.2)       // 20% weight for time to maturity
  );

  // Determine status based on score
  let status = 'excellent';
  if (overallScore < 20) {
    status = 'critical';
  } else if (overallScore < 40) {
    status = 'poor';
  } else if (overallScore < 60) {
    status = 'fair';
  } else if (overallScore < 80) {
    status = 'good';
  }

  return {
    score: overallScore,
    components: {
      paymentTimeliness: paymentTimelinessScore,
      amountPaid: amountPaidScore,
      timeToMaturity: timeToMaturityScore
    },
    status
  };
}
```

**Proposed Frontend Component**:
```tsx
// components/loan/HealthIndicator.tsx
export function HealthIndicator({ score, size = "md" }: { score: number, size?: "sm" | "md" | "lg" }) {
  // Determine color based on score
  const getColor = () => {
    if (score < 20) return "bg-red-500";
    if (score < 40) return "bg-orange-500";
    if (score < 60) return "bg-yellow-500";
    if (score < 80) return "bg-blue-500";
    return "bg-green-500";
  };

  // Determine size classes
  const getSizeClasses = () => {
    switch (size) {
      case "sm": return "w-6 h-6 text-xs";
      case "lg": return "w-16 h-16 text-xl";
      default: return "w-10 h-10 text-sm";
    }
  };

  return (
    <div className={`relative rounded-full ${getColor()} ${getSizeClasses()} flex items-center justify-center text-white font-medium`}>
      {score}
    </div>
  );
}
```

### 3. Available Credit Utilization Visualization

Add a visual representation of credit utilization to show users how much of their E600 limit they've used. This would encourage responsible borrowing and provide context for loan application decisions.

**Implementation Suggestions**:
- Add a circular or linear progress indicator
- Show used and available credit amounts
- Color-code based on utilization percentage
- Place at the top of the loan history page

**Proposed Implementation**:
```tsx
// components/loan/CreditUtilization.tsx
export function CreditUtilization() {
  const [creditInfo, setCreditInfo] = useState<{
    availableCredit: number;
    outstandingAmount: number;
    maxCreditLimit: number;
  }>({
    availableCredit: 0,
    outstandingAmount: 0,
    maxCreditLimit: 600
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchCreditInfo = async () => {
      try {
        setIsLoading(true);
        const response = await loanApi.getAvailableCredit();
        if (response.success) {
          setCreditInfo(response.data);
        }
      } catch (error) {
        console.error("Error fetching credit info:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCreditInfo();
  }, []);

  // Calculate utilization percentage
  const utilizationPercentage = Math.min(
    100,
    Math.round((creditInfo.outstandingAmount / creditInfo.maxCreditLimit) * 100)
  );

  // Determine color based on utilization
  const getUtilizationColor = () => {
    if (utilizationPercentage > 80) return "text-red-500";
    if (utilizationPercentage > 50) return "text-yellow-500";
    return "text-green-500";
  };

  if (isLoading) {
    return <div className="flex justify-center py-4"><Loader2 className="h-6 w-6 animate-spin" /></div>;
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Credit Utilization</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-2">
          <div>
            <p className="text-sm text-gray-500">Available Credit</p>
            <p className="text-xl font-bold">{formatCurrency(creditInfo.availableCredit)}</p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">Outstanding Amount</p>
            <p className="text-xl font-bold">{formatCurrency(creditInfo.outstandingAmount)}</p>
          </div>
        </div>

        {/* Progress bar */}
        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
          <div
            className={`h-2.5 rounded-full ${getUtilizationColor()}`}
            style={{ width: `${utilizationPercentage}%` }}
          ></div>
        </div>

        <div className="flex justify-between text-xs text-gray-500">
          <span>0%</span>
          <span className={getUtilizationColor()}>{utilizationPercentage}% used</span>
          <span>100%</span>
        </div>

        <p className="text-center mt-2 text-sm">
          Maximum Lifetime Limit: {formatCurrency(creditInfo.maxCreditLimit)}
        </p>
      </CardContent>
    </Card>
  );
}
```

### 4. Export and Download Options

Allow users to export their loan history in various formats (PDF, CSV). This would help users maintain personal financial records for tax purposes or loan refinancing.

**Implementation Suggestions**:
- Add export buttons for different formats
- Generate well-formatted documents with loan details
- Include summary statistics in exports
- Enhance the existing "Download" button functionality

**Proposed Implementation**:
```tsx
// utils/exportUtils.ts
export const exportToPDF = async (loans: Loan[]) => {
  // Import jsPDF dynamically to reduce bundle size
  const { jsPDF } = await import('jspdf');
  const { autoTable } = await import('jspdf-autotable');

  const doc = new jsPDF();

  // Add title
  doc.setFontSize(18);
  doc.text('Loan History Report', 14, 22);
  doc.setFontSize(11);
  doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 14, 30);

  // Prepare data for table
  const tableColumn = ["ID", "Amount", "Purpose", "Status", "Application Date", "Due Date"];
  const tableRows = loans.map(loan => [
    loan.id.substring(0, 8),
    formatCurrency(loan.amount),
    loan.purpose,
    loan.status,
    formatDate(loan.createdAt),
    formatDate(loan.dueDate)
  ]);

  // Add summary statistics
  const totalAmount = loans.reduce((sum, loan) => sum + loan.amount, 0);
  doc.text(`Total Loans: ${loans.length}`, 14, 40);
  doc.text(`Total Amount: ${formatCurrency(totalAmount)}`, 14, 48);

  // Add table
  autoTable(doc, {
    head: [tableColumn],
    body: tableRows,
    startY: 60,
    theme: 'striped',
    headStyles: { fillColor: [66, 139, 202] }
  });

  // Save the PDF
  doc.save('loan-history.pdf');
};

export const exportToCSV = (loans: Loan[]) => {
  // Prepare CSV content
  const headers = ["ID", "Amount", "Purpose", "Status", "Application Date", "Due Date", "Interest Rate"];

  const rows = loans.map(loan => [
    loan.id,
    loan.amount,
    loan.purpose,
    loan.status,
    loan.createdAt,
    loan.dueDate,
    loan.interestRate ? `${loan.interestRate}%` : 'N/A'
  ]);

  // Combine headers and rows
  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.join(','))
  ].join('\n');

  // Create download link
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', 'loan-history.csv');
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
```

### 5. Early Repayment Calculator

Add a tool that shows potential savings from early loan repayment. This would encourage users to pay off loans early when possible and help them make informed financial decisions.

**Implementation Suggestions**:
- Create a calculator that shows interest savings from early payments
- Allow users to input hypothetical payment amounts
- Show before/after comparisons of total repayment amounts
- Add a calculator button to each loan row

**Proposed Implementation**:
```tsx
// components/loan/EarlyRepaymentCalculator.tsx
export function EarlyRepaymentCalculator({ loan }: { loan: Loan }) {
  const [paymentAmount, setPaymentAmount] = useState<string>("");
  const [results, setResults] = useState<{
    originalTotal: number;
    newTotal: number;
    savings: number;
    newDueDate: Date | null;
  } | null>(null);

  const calculateSavings = () => {
    const amount = parseFloat(paymentAmount);
    if (isNaN(amount) || amount <= 0) return;

    // Calculate original total repayment
    const originalTotal = loan.amount + (loan.amount * loan.interestRate / 100);

    // Calculate remaining principal after payment
    const remainingPrincipal = Math.max(0, loan.amount - amount);

    // Calculate new total repayment
    const newTotal = remainingPrincipal + (remainingPrincipal * loan.interestRate / 100);

    // Calculate savings
    const savings = originalTotal - (amount + newTotal);

    // Calculate new due date (simplified)
    const now = new Date();
    const dueDate = new Date(loan.dueDate);
    const totalDays = (dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
    const remainingRatio = remainingPrincipal / loan.amount;
    const newDaysRemaining = totalDays * remainingRatio;

    const newDueDate = remainingPrincipal > 0
      ? new Date(now.getTime() + (newDaysRemaining * 1000 * 60 * 60 * 24))
      : null;

    setResults({
      originalTotal,
      newTotal: amount + newTotal,
      savings,
      newDueDate
    });
  };

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="font-medium mb-2">Early Repayment Calculator</h3>

      <div className="mb-4">
        <label className="block text-sm mb-1">Payment Amount</label>
        <div className="flex gap-2">
          <Input
            type="number"
            value={paymentAmount}
            onChange={(e) => setPaymentAmount(e.target.value)}
            placeholder="Enter amount"
            className="flex-1"
          />
          <Button onClick={calculateSavings}>Calculate</Button>
        </div>
      </div>

      {results && (
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Original Total:</span>
            <span>{formatCurrency(results.originalTotal)}</span>
          </div>
          <div className="flex justify-between">
            <span>New Total:</span>
            <span>{formatCurrency(results.newTotal)}</span>
          </div>
          <div className="flex justify-between font-medium">
            <span>Your Savings:</span>
            <span className="text-green-600">{formatCurrency(results.savings)}</span>
          </div>
          {results.newDueDate && (
            <div className="flex justify-between">
              <span>New Due Date:</span>
              <span>{formatDate(results.newDueDate.toISOString())}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
```

These recommendations provide a roadmap for enhancing the loan history page with features that improve usability, provide valuable insights, and encourage responsible financial behavior. Each suggested implementation includes code examples that can be adapted and integrated into the existing codebase.

## Conclusion

The Umlamleli Loan Management System has undergone significant improvements to enhance user experience, particularly in the loan history page. The implemented features and bug fixes have addressed key usability issues and laid the groundwork for future enhancements.

### Key Achievements

1. **Enhanced User Experience**
   - Improved mobile responsiveness with expandable rows and horizontal scrolling
   - Implemented consistent date formatting in "DD/MM/YYYY" format
   - Fixed status display issues for better clarity
   - Added advanced filtering and sorting capabilities

2. **Technical Improvements**
   - Resolved PostgreSQL enum type handling issues
   - Implemented proper error handling for database queries
   - Added localStorage persistence for user preferences
   - Enhanced code organization and reusability

3. **Foundation for Future Features**
   - Created a solid architecture for implementing payment schedules
   - Designed a framework for loan health indicators
   - Established patterns for data visualization components
   - Prepared the groundwork for export functionality

### Next Steps

The immediate next steps should focus on implementing the highest-priority recommendations:

1. **Payment Schedule Timeline** - This feature would provide immediate value to users by visualizing their payment obligations.
2. **Loan Health Indicators** - These indicators would help users understand their loan status at a glance.
3. **Available Credit Utilization** - This visualization would encourage responsible borrowing.

By continuing to enhance the Umlamleli Loan Management System with these user-focused features, the application will better serve its purpose of providing accessible and transparent microloans to users in Eswatini.
