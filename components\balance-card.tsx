"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { RefreshCw, TrendingUp, TrendingDown } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { loanApi } from "@/lib/api"
import { toast } from "sonner"

interface BalanceCardProps {
  isLoading?: boolean
}

export function BalanceCard({ isLoading = false }: BalanceCardProps) {
  const [balance, setBalance] = useState(20000.00) // Default fallback balance
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [trend, setTrend] = useState<"up" | "down">("up")
  const [changePercent, setChangePercent] = useState(2.5)
  const [currency, setCurrency] = useState("SZL") // For MTN sandbox testing

  // Fetch MTN balance on component mount
  useEffect(() => {
    fetchMTNBalance();
  }, []);

  // Simulate balance updates every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      // Random fluctuation between -1% and +1%
      const fluctuation = (Math.random() * 2 - 1) * 0.01
      const newBalance = balance * (1 + fluctuation)

      setTrend(fluctuation >= 0 ? "up" : "down")
      setChangePercent(Math.abs(fluctuation * 100).toFixed(2) as unknown as number)
      setBalance(newBalance)
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [balance])

  const fetchMTNBalance = async () => {
    try {
      const response = await loanApi.getMTNBalance();
      if (response.success && response.data) {
        const balanceAmount = parseFloat(response.data.availableBalance) || 20000.00;
        setBalance(balanceAmount);
        setCurrency(response.data.currency || "SZL");
      }
    } catch (error) {
      console.error('Failed to fetch MTN balance:', error);
      // Keep default balance of 20000 if API fails
      //toast.error("Failed to fetch MTN balance", {
        //description: "Using simulated balance"
      //});
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true)

    try {
      await fetchMTNBalance();
      toast.success("Balance refreshed successfully");
    } catch (error) {
      // Simulate API call fallback
      setTimeout(() => {
        // Random fluctuation between -2% and +2%
        const fluctuation = (Math.random() * 4 - 2) * 0.01
        const newBalance = balance * (1 + fluctuation)

        setTrend(fluctuation >= 0 ? "up" : "down")
        setChangePercent(Math.abs(fluctuation * 100).toFixed(2) as unknown as number)
        setBalance(newBalance)
      }, 1500)
    } finally {
      setIsRefreshing(false)
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <Skeleton className="h-4 w-[140px]" />
          <Skeleton className="h-3 w-[100px]" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-[120px] mb-2" />
          <Skeleton className="h-3 w-[80px]" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-sm font-medium">Mobile Money Balance</CardTitle>
          <CardDescription>Collections Account</CardDescription>
        </div>
        <Button variant="ghost" size="icon" onClick={handleRefresh} disabled={isRefreshing}>
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
        </Button>
      </CardHeader>
      <CardContent>
        <div className="flex items-baseline space-x-2">
          <div className="text-2xl font-bold">
            {currency === "EUR" ? "€" : "E"}{balance.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </div>
          <div className={`flex items-center text-xs ${trend === "up" ? "text-green-500" : "text-red-500"}`}>
            {trend === "up" ? <TrendingUp className="h-3 w-3 mr-1" /> : <TrendingDown className="h-3 w-3 mr-1" />}
            {changePercent}%
          </div>
        </div>
        <p className="text-xs text-muted-foreground mt-1">Updated just now</p>
      </CardContent>
    </Card>
  )
}

