"use client"

import { toast } from "sonner";

// Make sure we have the correct API URL
export const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';
console.log('🌐 API URL configured as:', API_URL);

// Check if the backend is reachable on page load
(async function checkBackendConnection() {
  try {
    const controller = new AbortController();
    // Set 5 second timeout
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    console.log('⏳ Checking backend connection at:', `${API_URL}/health`);

    const response = await fetch(`${API_URL}/health`, {
      method: 'GET',
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Backend connection successful:', data);

    } else {
      console.warn('⚠️ Backend health check failed:', response.status, response.statusText);
      toast.warning('Backend connection issue detected', {
        description: 'Some features may not work correctly'
      });
    }
  } catch (error) {
    console.error('⛔ Cannot connect to backend server:', error);
    toast.error('Cannot connect to server', {
      description: 'Check that the backend server is running'
    });
  }
})();

// Helper for handling API responses and errors
const handleResponse = async (response: Response) => {
  if (!response.ok) {
    const error = await response.json().catch(() => ({ message: 'An unknown error occurred' }));
    throw new Error(error.message || 'Request failed');
  }
  return response.json();
};

// Auth API calls
export const authApi = {
  login: async (studentId: string, password: string) => {
    try {
      console.log('🔑 Login request:', { userId: studentId });

      const payload = { userId: studentId, password };
      console.log('Login payload:', JSON.stringify(payload));

      const requestInfo = {
        url: `${API_URL}/auth/login`,
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      };
      console.log('Sending login request to:', requestInfo.url);

      // Testing fetch directly to see if it works
      try {
        const response = await fetch(requestInfo.url, {
          method: requestInfo.method,
          headers: requestInfo.headers,
          body: requestInfo.body,
        });

        console.log('Login response status:', response.status, response.statusText);

        // Try to read the response as text first
        const responseText = await response.text();
        console.log('Login response text:', responseText);

        // Parse the response if possible
        let data;
        try {
          data = responseText ? JSON.parse(responseText) : {};
          console.log('Login response data:', data);
        } catch (jsonError) {
          console.error('Failed to parse JSON response:', jsonError);
          data = { success: false, message: 'Failed to parse server response' };
        }

        // Always check if data.success is true, not just the HTTP status
        if (!response.ok || !data.success) {
          // If the server returned a proper error message, use it
          const errorMessage = (data && data.message) ? data.message : `Server error: ${response.status} ${response.statusText}`;
          console.error('Login failed:', errorMessage);
          toast.error('Login failed', {
            description: errorMessage
          });
          throw new Error(errorMessage);
        }

        // If we got here, it's a success!
        console.log('Login success response:', data);

        // Store token in localStorage
        if (data.data && data.data.token) {
          localStorage.setItem('token', data.data.token);
          console.log('🔒 Token stored in localStorage');

          // Store user data
          if (data.data.user) {
            localStorage.setItem('user', JSON.stringify(data.data.user));
            console.log('👤 User data stored in localStorage');
          }
        } else {
          console.warn('⚠️ No token found in login response:', data);
        }

        // Show success message
        toast.success('Login successful', {
          description: data.data.passwordChangeRequired
            ? 'Please change your default password'
            : 'Welcome back!'
        });

        return data;
      } catch (fetchError) {
        console.error('Fetch error during login:', fetchError);
        throw fetchError;
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  register: async (userData: any) => {
    try {
      // Ensure we have all required fields
      if (!userData.name || !userData.userId || !userData.email || !userData.phoneNumber) {
        console.error('Registration validation failed:', userData);
        throw new Error('Missing required fields for registration');
      }

      console.log('API sending registration data:', userData);

      // Default password - add it explicitly
      const dataToSend = {
        ...userData,
        password: 'password123' // Add default password explicitly
      };

      console.log('Final registration payload:', dataToSend);

      try {
        const response = await fetch(`${API_URL}/auth/register`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(dataToSend),
        });

        // Log raw response for debugging
        console.log('Registration raw response status:', response.status);

        // If the response is not OK, handle it specially
        if (!response.ok) {
          let errorMessage = `Server error: ${response.status} ${response.statusText}`;

          // Safely try to parse the error response
          try {
            const textResponse = await response.text();
            console.log('Error response text:', textResponse);

            if (textResponse) {
              try {
                const errorData = JSON.parse(textResponse);
                console.error('Registration server error response:', errorData);
                if (errorData && typeof errorData === 'object' && 'message' in errorData) {
                  errorMessage = errorData.message || errorMessage;
                }
              } catch (jsonError) {
                console.error('Failed to parse JSON from error response:', jsonError);
              }
            }
          } catch (parseError) {
            console.error('Failed to parse error response:', parseError);
          }

          toast.error('Registration failed', {
            description: errorMessage
          });

          throw new Error(errorMessage);
        }

        // Parse successful response
        const data = await response.json();
        console.log('Registration success response:', data);

        // Show success message
        toast.success('Registration successful', {
          description: 'You can now login with your credentials'
        });

        return data;
      } catch (fetchError: any) {
        // Handle network errors or connection issues
        if (fetchError.message.includes('Failed to fetch') || !fetchError.response) {
          console.error('Network error during registration:', fetchError);
          throw new Error('Cannot connect to server. Please check your internet connection or try again later.');
        }
        throw fetchError;
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      throw error;
    }
  },

  changePassword: async (currentPassword: string, newPassword: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/auth/change-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ currentPassword, newPassword }),
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  },

  uploadProfileImage: async (imageFile: File) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      // Convert the file to base64
      const base64Image = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(imageFile);
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = error => reject(error);
      });

      const response = await fetch(`${API_URL}/profile/image`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ imageData: base64Image }),
      });

      const result = await handleResponse(response);

      // Update user data in localStorage if upload was successful
      if (result.success && result.data && result.data.profileImage) {
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
        const updatedUser = { ...currentUser, profileImage: result.data.profileImage };
        localStorage.setItem('user', JSON.stringify(updatedUser));
      }

      return result;
    } catch (error) {
      console.error('Upload profile image error:', error);
      throw error;
    }
  },

  updateProfile: async (profileData: any) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/profile`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(profileData),
      });

      const result = await handleResponse(response);

      // Update user data in localStorage if update was successful
      if (result.success && result.data) {
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
        const updatedUser = { ...currentUser, ...result.data };
        localStorage.setItem('user', JSON.stringify(updatedUser));
      }

      return result;
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  },

  verifyFace: async (faceDescriptor: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const userId = user.id;

      if (!userId) throw new Error('User ID not found');

      const response = await fetch(`${API_URL}/auth/verify-face/${userId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ faceDescriptor }),
      });

      const result = await handleResponse(response);

      // Update user data in localStorage if verification was successful
      if (result.success) {
        // Update both the isFaceVerified flag and the faceDescriptor
        const updatedUser = {
          ...user,
          isFaceVerified: true,
          faceDescriptor: faceDescriptor // Store the face descriptor in the user object
        };
        localStorage.setItem('user', JSON.stringify(updatedUser));
        console.log('Updated user in localStorage with face descriptor');
      }

      return result;
    } catch (error) {
      console.error('Face verification error:', error);
      throw error;
    }
  }
};

// Loan API calls
export const loanApi = {
  getAvailableCredit: async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/loans/available-credit`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get available credit error:', error);
      throw error;
    }
  },

  getLoanPaymentSchedule: async (loanId: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/loans/my-loans/${loanId}/payment-schedule`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get payment schedule error:', error);
      throw error;
    }
  },

  getLoanHealthScore: async (loanId: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/loans/my-loans/${loanId}/health-score`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get loan health score error:', error);
      throw error;
    }
  },

  getUserLoansFiltered: async (filters: any) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      // Build query string from filters
      const queryParams = new URLSearchParams();

      if (filters.status) queryParams.append('status', filters.status);
      if (filters.minAmount) queryParams.append('minAmount', filters.minAmount.toString());
      if (filters.maxAmount) queryParams.append('maxAmount', filters.maxAmount.toString());
      if (filters.startDate) queryParams.append('startDate', filters.startDate);
      if (filters.endDate) queryParams.append('endDate', filters.endDate);
      if (filters.search) queryParams.append('search', filters.search);

      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';

      const response = await fetch(`${API_URL}/loans/my-loans${queryString}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get filtered loans error:', error);
      throw error;
    }
  },

  applyForLoan: async (loanData: any) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/loans/apply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(loanData),
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Loan application error:', error);
      throw error;
    }
  },

  getUserLoans: async (filters?: {
    status?: string;
    minAmount?: number;
    maxAmount?: number;
    startDate?: string;
    endDate?: string;
    search?: string;
  }) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      // Build query string from filters if provided
      let queryString = '';
      if (filters) {
        const queryParams = new URLSearchParams();

        if (filters.status) queryParams.append('status', filters.status);
        if (filters.minAmount) queryParams.append('minAmount', filters.minAmount.toString());
        if (filters.maxAmount) queryParams.append('maxAmount', filters.maxAmount.toString());
        if (filters.startDate) queryParams.append('startDate', filters.startDate);
        if (filters.endDate) queryParams.append('endDate', filters.endDate);
        if (filters.search) queryParams.append('search', filters.search);

        queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      }

      const response = await fetch(`${API_URL}/loans/my-loans${queryString}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get user loans error:', error);
      throw error;
    }
  },

  getLoanById: async (loanId: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/loans/my-loans/${loanId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get loan details error:', error);
      throw error;
    }
  },

  makeRepayment: async (loanId: string, amount: number, payerPhoneNumber?: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const requestBody: any = { amount };
      if (payerPhoneNumber) {
        requestBody.payerPhoneNumber = payerPhoneNumber;
      }

      const response = await fetch(`${API_URL}/loans/my-loans/${loanId}/repay`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody),
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Loan repayment error:', error);
      throw error;
    }
  },

  getLoanStatistics: async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/loans/statistics`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get loan statistics error:', error);
      throw error;
    }
  },

  getActiveLoans: async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/loans/active`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get active loans error:', error);
      throw error;
    }
  },

  // Get simulated account balance (admin only)
  getSimulatedBalance: async () => {
    try {
      const token = localStorage.getItem('token') || localStorage.getItem('adminToken');
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/loans/simulated-balance`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get simulated balance error:', error);
      throw error;
    }
  }
};

// Notification API calls
export const notificationApi = {
  getUserNotifications: async (page = 1, limit = 10, read?: boolean) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      let url = `${API_URL}/notifications/user/my-notifications?page=${page}&limit=${limit}`;

      if (read !== undefined) {
        url += `&read=${read}`;
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get user notifications error:', error);
      throw error;
    }
  },

  markAsRead: async (notificationId: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/notifications/user/${notificationId}/read`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Mark notification as read error:', error);
      throw error;
    }
  },

  markAllAsRead: async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/notifications/user/read-all`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Mark all notifications as read error:', error);
      throw error;
    }
  }
};

// OTP API calls
export const otpApi = {
  sendOtp: async (phoneNumber: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/otp/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ phoneNumber }),
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Send verification code error:', error);
      throw error;
    }
  },

  verifyOtp: async (phoneNumber: string, otpCode: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/otp/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ phoneNumber, otpCode }),
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Verify code error:', error);
      throw error;
    }
  },

  resendOtp: async (phoneNumber: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/otp/resend`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ phoneNumber }),
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Resend verification code error:', error);
      throw error;
    }
  }
};

// Transaction API calls
export const transactionApi = {
  getUserTransactions: async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/transactions/my-transactions`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get user transactions error:', error);
      throw error;
    }
  },

  getTransactionById: async (transactionId: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/transactions/my-transactions/${transactionId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get transaction details error:', error);
      throw error;
    }
  },

  createDeposit: async (amount: number, reference?: string, description?: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/transactions/deposit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ amount, reference, description }),
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Create deposit error:', error);
      throw error;
    }
  },

  createWithdrawal: async (amount: number, reference?: string, description?: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/transactions/withdraw`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ amount, reference, description }),
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Create withdrawal error:', error);
      throw error;
    }
  },

  getTransactionStatistics: async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/transactions/statistics`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get transaction statistics error:', error);
      throw error;
    }
  }
};

// Admin API calls
export const adminApi = {
  // Loan management
  getPendingLoans: async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/loans/pending`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get pending loans error:', error);
      throw error;
    }
  },

  approveLoan: async (loanId: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/loans/${loanId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Approve loan error:', error);
      throw error;
    }
  },

  rejectLoan: async (loanId: string, reason?: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/loans/${loanId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ reason }),
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Reject loan error:', error);
      throw error;
    }
  },

  disburseLoan: async (loanId: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/loans/${loanId}/disburse`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Disburse loan error:', error);
      throw error;
    }
  },

  // Transaction management
  updateTransactionStatus: async (transactionId: string, status: string, metadata?: any, failureReason?: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/transactions/${transactionId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status, metadata, failureReason }),
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Update transaction status error:', error);
      throw error;
    }
  }
};

// Helper function to get the appropriate token
const getAuthToken = (): string | null => {
  // Check for admin token first (for admin users)
  const adminToken = localStorage.getItem('adminToken');
  if (adminToken) return adminToken;

  // Fall back to regular token (for regular users)
  const token = localStorage.getItem('token');
  return token;
};

// Document API calls
export const documentApi = {
  // Get all documents (admin only)
  getAllDocuments: async () => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/documents`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get all documents error:', error);
      throw error;
    }
  },

  // Get documents by user ID
  getDocumentsByUserId: async (userId: string) => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/documents/user/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get documents by user ID error:', error);
      throw error;
    }
  },

  // Get documents by loan ID
  getDocumentsByLoanId: async (loanId: string) => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/documents/loan/${loanId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get documents by loan ID error:', error);
      throw error;
    }
  },

  // Get document by ID
  getDocumentById: async (documentId: string) => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/documents/${documentId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get document by ID error:', error);
      throw error;
    }
  },

  // Upload document
  uploadDocument: async (file: File, documentType: string, userId?: string, loanId?: string) => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in');

      const formData = new FormData();
      formData.append('file', file);
      formData.append('documentType', documentType);

      if (userId) {
        formData.append('userId', userId);
      }

      if (loanId) {
        formData.append('loanId', loanId);
      }

      const response = await fetch(`${API_URL}/documents/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData,
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Upload document error:', error);
      throw error;
    }
  },

  // Update document status (admin only)
  updateDocumentStatus: async (documentId: string, status: string, rejectionReason?: string) => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/documents/${documentId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status, rejectionReason }),
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Update document status error:', error);
      throw error;
    }
  },

  // Delete document
  deleteDocument: async (documentId: string) => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/documents/${documentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Delete document error:', error);
      throw error;
    }
  },

  // Download document
  downloadDocument: async (documentId: string) => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in');

      const response = await fetch(`${API_URL}/documents/${documentId}/download`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Download failed' }));
        throw new Error(error.message || 'Download failed');
      }

      // Return the blob for download
      const blob = await response.blob();
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = 'document';

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      return { blob, filename };
    } catch (error) {
      console.error('Download document error:', error);
      throw error;
    }
  }
};

// Reports API calls
export const reportsApi = {
  // Get all reports
  getAllReports: async (period: string = 'month') => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/reports?period=${period}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get all reports error:', error);
      throw error;
    }
  },

  // Get financial reports
  getFinancialReports: async (period: string = 'month') => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/reports/financial?period=${period}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get financial reports error:', error);
      throw error;
    }
  },

  // Get user reports
  getUserReports: async (period: string = 'month') => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/reports/user?period=${period}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get user reports error:', error);
      throw error;
    }
  },

  // Get loan reports
  getLoanReports: async (period: string = 'month') => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/reports/loan?period=${period}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get loan reports error:', error);
      throw error;
    }
  },

  // Get operational reports
  getOperationalReports: async (period: string = 'month') => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/reports/operational?period=${period}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get operational reports error:', error);
      throw error;
    }
  },

  // Get specific report
  getSpecificReport: async (reportType: string, reportName: string, period: string = 'month') => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/reports/${reportType}/${reportName}?period=${period}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });
      return handleResponse(response);
    } catch (error) {
      console.error('Get specific report error:', error);
      throw error;
    }
  },

  // Export report
  exportReport: async (reportType: string, reportName: string, period: string = 'month', format: string = 'json') => {
    try {
      const token = getAuthToken();
      if (!token) throw new Error('You must be logged in as admin');

      const response = await fetch(`${API_URL}/reports/${reportType}/${reportName}/export?period=${period}&format=${format}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Export failed' }));
        throw new Error(error.message || 'Export failed');
      }

      // Return the blob for download
      const blob = await response.blob();
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = `report-${reportType}-${reportName}-${period}`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      return { blob, filename };
    } catch (error) {
      console.error('Export report error:', error);
      throw error;
    }
  }
};