"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Link from "next/link"
import { AlertCircle, ArrowLeft, CheckCircle, CreditCard, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { loanApi } from "@/lib/api"
import { toast } from "sonner"

interface Loan {
  id: string;
  amount: number;
  purpose: string;
  status: string;
  dueDate: string;
  interestRate: number;
  amountPaid: number;
  // Balance could come from the API
  balance?: number;
}

export default function LoanPaymentPage() {
  const [activeLoans, setActiveLoans] = useState<Loan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedLoanId, setSelectedLoanId] = useState<string>("");
  const [paymentAmount, setPaymentAmount] = useState<string>("");
  const [paymentMethod, setPaymentMethod] = useState<string>("mobile_money");
  const [phoneNumber, setPhoneNumber] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [paymentError, setPaymentError] = useState<string | null>(null);

  useEffect(() => {
    const fetchActiveLoans = async () => {
      try {
        setIsLoading(true);
        const response = await loanApi.getUserLoans();
        if (response.success) {
          console.log("Fetched loans:", response.data);
          const activeLoans = response.data?.filter((loan: Loan) =>
            loan.status.toLowerCase() === 'active' ||
            loan.status.toLowerCase() === 'approved' ||
            loan.status.toLowerCase() === 'disbursed'
          ) || [];
          setActiveLoans(activeLoans);

          // Auto-select the first loan if there's only one
          if (activeLoans.length === 1) {
            setSelectedLoanId(activeLoans[0].id);
          }
        } else {
          console.error("Failed to fetch active loans:", response.message);
          setError(response.message || "Failed to load your active loans");
          toast.error("Could not load loans", {
            description: response.message || "Please try again later"
          });
        }
      } catch (err: any) {
        console.error("Error fetching active loans:", err);
        setError(err.message || "An error occurred while loading your loans");
        toast.error("Error loading loans", {
          description: err.message || "Please try refreshing the page"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchActiveLoans();
  }, []);

  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setPaymentError(null);
    setSuccessMessage(null);

    // Basic validation
    if (!selectedLoanId) {
      setPaymentError("Please select a loan to pay");
      return;
    }

    const amount = parseFloat(paymentAmount);
    if (isNaN(amount) || amount <= 0) {
      setPaymentError("Please enter a valid payment amount");
      return;
    }

    // Validate phone number for mobile money payments
    if (paymentMethod === "mobile_money" && !phoneNumber.trim()) {
      setPaymentError("Please enter your phone number for mobile money payment");
      return;
    }

    try {
      setIsSubmitting(true);

      // Prepare payment data
      const paymentData: any = {
        amount: amount
      };

      // Add phone number for mobile money payments
      if (paymentMethod === "mobile_money") {
        paymentData.payerPhoneNumber = phoneNumber.trim();
      }

      const response = await loanApi.makeRepayment(selectedLoanId, paymentData.amount, paymentData.payerPhoneNumber);

      if (response.success) {
        console.log("Payment successful:", response.data);
        setSuccessMessage("Payment processed successfully!");
        setPaymentAmount("");

        // Refresh loan list to update balances
        const loansResponse = await loanApi.getUserLoans();
        if (loansResponse.success) {
          const activeLoans = loansResponse.data?.filter((loan: Loan) =>
            loan.status.toLowerCase() === 'active' ||
            loan.status.toLowerCase() === 'approved' ||
            loan.status.toLowerCase() === 'disbursed'
          ) || [];
          setActiveLoans(activeLoans);
        }

        toast.success("Payment successful", {
          description: `Your payment of E${amount.toFixed(2)} has been processed.`
        });
      } else {
        console.error("Payment failed:", response.message);
        setPaymentError(response.message || "Payment could not be processed");
        toast.error("Payment failed", {
          description: response.message || "Please try again later"
        });
      }
    } catch (err: any) {
      console.error("Error processing payment:", err);
      setPaymentError(err.message || "An error occurred during payment processing");
      toast.error("Payment error", {
        description: err.message || "Please try again later"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSelectedLoan = () => {
    return activeLoans.find(loan => loan.id === selectedLoanId);
  };

  const formatCurrency = (amount: number) => {
    return `E${amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  // Calculate total amount due (principal + interest)
  const calculateTotalAmountDue = (loan: Loan) => {
    const principal = Number(loan.amount) || 0;
    const rate = Number(loan.interestRate) || 0;
    const interestAmount = (principal * rate) / 100;
    return principal + interestAmount;
  };

  // Calculate outstanding amount (total due - amount paid)
  const calculateOutstandingAmount = (loan: Loan) => {
    const totalDue = calculateTotalAmountDue(loan);
    const amountPaid = Number(loan.amountPaid) || 0;
    return Math.max(0, totalDue - amountPaid);
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch (e) {
      return "Invalid date";
    }
  };

  return (
    <div className="min-h-screen bg-blue-50 py-8 px-4 overflow-auto">
      <div className="container mx-auto max-w-md">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-blue-900">Loan Payment</h1>
          <Button asChild variant="outline">
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Make a Payment</CardTitle>
            <CardDescription>Pay off your active loans</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex flex-col items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-4" />
                <p className="text-blue-500">Loading your loans...</p>
              </div>
            ) : error ? (
              <div className="text-center py-6">
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                >
                  Try Again
                </Button>
              </div>
            ) : activeLoans.length === 0 ? (
              <div className="text-center py-6">
                <p className="mb-4">You don't have any active loans to pay off.</p>
                <Button asChild className="bg-blue-600 hover:bg-blue-700">
                  <Link href="/loan/apply">Apply for a Loan</Link>
                </Button>
              </div>
            ) : (
              <form onSubmit={handlePaymentSubmit} className="space-y-6">
                {paymentError && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    <AlertDescription>{paymentError}</AlertDescription>
                  </Alert>
                )}

                {successMessage && (
                  <Alert className="bg-green-50 border-green-200">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    <AlertDescription className="text-green-700">{successMessage}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                  <Label htmlFor="loan-select">Select Loan</Label>
                  <Select
                    value={selectedLoanId}
                    onValueChange={setSelectedLoanId}
                    disabled={isSubmitting}
                  >
                    <SelectTrigger id="loan-select">
                      <SelectValue placeholder="Select a loan to pay" />
                    </SelectTrigger>
                    <SelectContent>
                      {activeLoans.map((loan) => (
                        <SelectItem key={loan.id} value={loan.id}>
                          {loan.purpose} - {formatCurrency(loan.amount)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
              </div>

                {selectedLoanId && getSelectedLoan() && (() => {
                  const selectedLoan = getSelectedLoan()!;
                  const totalAmountDue = calculateTotalAmountDue(selectedLoan);
                  const outstandingAmount = calculateOutstandingAmount(selectedLoan);
                  const interestAmount = totalAmountDue - selectedLoan.amount;

                  return (
                    <div className="rounded-lg bg-blue-50 p-4 border border-blue-100">
                      <h3 className="font-medium text-blue-900 mb-2">Loan Details</h3>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="text-blue-700">Principal Amount:</div>
                        <div>{formatCurrency(selectedLoan.amount)}</div>
                        <div className="text-blue-700">Interest ({selectedLoan.interestRate}%):</div>
                        <div>{formatCurrency(interestAmount)}</div>
                        <div className="text-blue-700">Total Amount Due:</div>
                        <div className="font-semibold">{formatCurrency(totalAmountDue)}</div>
                        <div className="text-blue-700">Amount Paid:</div>
                        <div>{formatCurrency(selectedLoan.amountPaid)}</div>
                        <div className="text-blue-700">Outstanding Balance:</div>
                        <div className="font-semibold text-red-600">{formatCurrency(outstandingAmount)}</div>
                        <div className="text-blue-700">Due Date:</div>
                        <div>{formatDate(selectedLoan.dueDate)}</div>
                      </div>
                    </div>
                  );
                })()}

                <div className="space-y-2">
                  <Label htmlFor="payment-method">Payment Method</Label>
                  <Select
                    value={paymentMethod}
                    onValueChange={setPaymentMethod}
                    disabled={isSubmitting}
                  >
                    <SelectTrigger id="payment-method">
                      <SelectValue placeholder="Select payment method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mobile_money">Mobile Money (MTN)</SelectItem>
                      <SelectItem value="cash">Cash Payment</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {paymentMethod === "mobile_money" && (
                  <div className="space-y-2">
                    <Label htmlFor="phone-number">Phone Number</Label>
                    <Input
                      id="phone-number"
                      type="tel"
                      placeholder="e.g., 46733123453"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      disabled={isSubmitting}
                    />
                    <p className="text-xs text-gray-500">
                      Enter your MTN Mobile Money number for payment processing
                    </p>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="payment-amount">Payment Amount</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                      E
                    </span>
                  <Input
                      id="payment-amount"
                      type="number"
                      placeholder="0.00"
                      className="pl-8"
                      value={paymentAmount}
                      onChange={(e) => setPaymentAmount(e.target.value)}
                      disabled={isSubmitting || !selectedLoanId}
                      step="0.01"
                      min="0.01"
                    />
                  </div>
                  {/*<p className="text-xs text-gray-500">
                    Amount will be processed in EUR for MTN sandbox testing
                  </p>*/}
                </div>

                <Button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700"
                  disabled={isSubmitting || !selectedLoanId || !paymentAmount}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing Payment...
                    </>
                  ) : (
                    <>
                      <CreditCard className="mr-2 h-4 w-4" />
                      Make Payment
                    </>
                  )}
                </Button>
            </form>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

