# Umlamleli Technical Architecture

## Overview

This document provides a detailed technical architecture of the Umlamleli loan management web application. It covers the technology stack, system components, data flow, and integration points.

## Technology Stack

### Frontend
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **UI Components**: Custom components built with Tailwind CSS and shadcn/ui
- **State Management**: React Context API
- **Form Handling**: React Hook Form
- **Animations**: Framer Motion
- **Face Detection**: face-api.js

### Backend
- **Framework**: Express.js
- **Language**: TypeScript
- **ORM**: TypeORM
- **Authentication**: JWT (JSON Web Tokens)
- **Password Hashing**: bcrypt
- **Validation**: express-validator
- **SMS Integration**: Twilio API

### Database
- **RDBMS**: PostgreSQL
- **Connection Pooling**: Built-in TypeORM pooling

### DevOps
- **Version Control**: Git
- **CI/CD**: GitHub Actions (recommended)
- **Containerization**: Docker (optional)
- **Process Management**: PM2 (production)

## System Components

### Frontend Components

#### Core Components
1. **Layout Components**
   - `BackgroundWrapper`: Provides consistent background styling
   - `MainNavbar`: Main navigation for public pages
   - `AuthNavbar`: Navigation for authentication pages
   - `AdminHeader`: Header for admin pages

2. **Authentication Components**
   - `LoginForm`: Handles user login
   - `RegistrationForm`: Handles user registration
   - `ChangePasswordForm`: Manages password changes
   - `AuthGuard`: Protects routes requiring authentication

3. **Loan Components**
   - `LoanApplicationForm`: Multi-step loan application
   - `FaceScan`: Facial recognition and liveness detection
   - `OtpVerification`: Phone verification via SMS
   - `LoanSummary`: Displays loan details
   - `LoanConfirmation`: Final review before submission

4. **Dashboard Components**
   - `UserNav`: User navigation and profile menu
   - `NotificationBell`: Notification indicator and dropdown
   - `LoanSummary`: Overview of active loans
   - `RecentTransactions`: List of recent transactions

5. **Admin Components**
   - `AdminNav`: Admin navigation
   - `LoanManagement`: Tools for managing loans
   - `UserManagement`: Tools for managing users
   - `AdminNotifications`: Admin notification system

#### UI Components
- Buttons, Cards, Inputs, Modals, Tabs, Tables, etc.
- Custom animations and transitions
- Responsive design elements

### Backend Components

#### API Routes
1. **Authentication Routes**
   - `/api/auth/register`: User registration
   - `/api/auth/login`: User login
   - `/api/auth/change-password`: Password management
   - `/api/auth/verify-face`: Face verification

2. **Loan Routes**
   - `/api/loans/apply`: Loan application
   - `/api/loans/my-loans`: User's loans
   - `/api/loans/:id/repay`: Loan repayment
   - `/api/loans/pending`: Admin pending loans
   - `/api/loans/:id/approve`: Loan approval
   - `/api/loans/:id/reject`: Loan rejection
   - `/api/loans/:id/disburse`: Loan disbursement

3. **User Routes**
   - `/api/profile`: User profile management
   - `/api/profile/image`: Profile image upload

4. **Notification Routes**
   - `/api/notifications/user/my-notifications`: User notifications
   - `/api/notifications/user/:id/read`: Mark notification as read
   - `/api/notifications/user/read-all`: Mark all as read

5. **OTP Routes**
   - `/api/otp/send`: Send verification code
   - `/api/otp/verify`: Verify OTP code
   - `/api/otp/resend`: Resend verification code

#### Services
1. **AuthService**: Handles authentication logic
2. **LoanService**: Manages loan processing
3. **UserService**: User management
4. **NotificationService**: Notification handling
5. **OtpService**: OTP generation and verification
6. **TransactionService**: Financial transaction processing

#### Middleware
1. **Authentication Middleware**: Validates JWT tokens
2. **Error Handling Middleware**: Centralized error handling
3. **Validation Middleware**: Input validation
4. **Logging Middleware**: Request/response logging
5. **Rate Limiting Middleware**: Prevents abuse

### Database Schema

#### User Table
- `id`: UUID (Primary Key)
- `fullName`: String
- `email`: String (Unique)
- `phoneNumber`: String (Unique)
- `studentId`: String (Unique, Optional)
- `password`: String (Hashed)
- `role`: Enum (CUSTOMER, ADMIN, STAFF)
- `status`: Enum (ACTIVE, INACTIVE, SUSPENDED)
- `faceDescriptor`: ByteA (Face recognition data)
- `isFaceVerified`: Boolean
- `isEmailVerified`: Boolean
- `isPhoneVerified`: Boolean
- `passwordChanged`: Boolean
- `lastLoginAt`: DateTime
- `createdAt`: DateTime
- `updatedAt`: DateTime

#### Loan Table
- `id`: UUID (Primary Key)
- `userId`: UUID (Foreign Key)
- `amount`: Decimal
- `interestRate`: Decimal
- `term`: Integer (in months)
- `purpose`: String
- `status`: Enum (PENDING, APPROVED, REJECTED, DISBURSED, PAID, OVERDUE)
- `rejectionReason`: String (Optional)
- `approvedAt`: DateTime (Optional)
- `disbursedAt`: DateTime (Optional)
- `dueDate`: DateTime
- `createdAt`: DateTime
- `updatedAt`: DateTime

#### Transaction Table
- `id`: UUID (Primary Key)
- `userId`: UUID (Foreign Key)
- `loanId`: UUID (Foreign Key, Optional)
- `type`: Enum (DEPOSIT, WITHDRAWAL, REPAYMENT, DISBURSEMENT)
- `amount`: Decimal
- `status`: Enum (PENDING, COMPLETED, FAILED)
- `reference`: String
- `description`: String
- `createdAt`: DateTime
- `updatedAt`: DateTime

#### Notification Table
- `id`: UUID (Primary Key)
- `recipientId`: UUID (Foreign Key, Optional)
- `createdById`: UUID (Foreign Key, Optional)
- `title`: String
- `message`: String
- `type`: Enum (SYSTEM, LOAN, PAYMENT, USER, ALERT)
- `status`: Enum (DRAFT, SENT, SCHEDULED)
- `isRead`: Boolean
- `isAllUsers`: Boolean
- `scheduledFor`: DateTime (Optional)
- `createdAt`: DateTime
- `updatedAt`: DateTime

#### OTP Table
- `id`: UUID (Primary Key)
- `phoneNumber`: String
- `code`: String
- `expiresAt`: DateTime
- `isVerified`: Boolean
- `createdAt`: DateTime

## Data Flow

### Authentication Flow
1. User submits credentials
2. Backend validates credentials
3. If valid, generates JWT token
4. Returns token and user data to frontend
5. Frontend stores token in localStorage
6. Frontend uses token for subsequent API calls

### Loan Application Flow
1. User fills out loan application form
2. Frontend validates inputs
3. User completes OTP verification
4. User completes face verification
5. Application data sent to backend
6. Backend validates and stores application
7. Creates notification for admin review
8. Returns confirmation to user

### Admin Approval Flow
1. Admin reviews loan application
2. Makes approval/rejection decision
3. Backend updates loan status
4. Creates notification for user
5. If approved, prepares for disbursement

### Notification Flow
1. System event triggers notification creation
2. Notification stored in database
3. Real-time update sent to recipient if online
4. User views notification in notification center
5. Marks as read when viewed

## Integration Points

### External Services

#### Twilio Integration
- Used for SMS-based OTP verification
- Integration point: OtpService
- Configuration: API keys in environment variables

#### Payment Gateway (Future)
- For processing loan disbursements and repayments
- Integration point: TransactionService
- Configuration: API keys and endpoints

### Internal Integration

#### Face Recognition
- Integration between frontend face-api.js and backend storage
- Face descriptors stored in user records
- Verification performed on both client and server

#### Notification System
- Integration between various services and notification service
- Event-based triggers for notifications
- Admin and user notification channels

## Deployment Architecture

### Development Environment
- Local Node.js server
- Local PostgreSQL database
- Environment variables for configuration

### Production Environment (Recommended)
- Web Server: Nginx (reverse proxy)
- Application Server: Node.js with PM2
- Database Server: PostgreSQL
- SSL/TLS encryption
- Regular database backups
- Monitoring and logging

## Security Architecture

### Authentication Security
- JWT with appropriate expiration
- Secure password storage with bcrypt
- Multi-factor authentication (OTP)
- Biometric verification (Face)

### Data Security
- Input validation on all endpoints
- Parameterized queries via TypeORM
- HTTPS for all communications
- Sensitive data encryption

### Application Security
- CSRF protection
- XSS prevention
- Rate limiting
- Security headers
- Regular dependency updates
