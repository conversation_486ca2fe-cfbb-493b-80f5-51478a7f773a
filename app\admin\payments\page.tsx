"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, Download, MoreHorizontal, Filter, ArrowUpRight, ArrowDownRight, Loader2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { PaymentChart } from "@/components/payment-chart"
import { toast } from "sonner"
import adminTransactionsAPI, {
  Transaction,
  TransactionStats,
  TransactionFilters
} from "@/lib/admin-transactions-api"

export default function PaymentsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [stats, setStats] = useState<TransactionStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isExporting, setIsExporting] = useState(false)

  // Fetch transactions data
  const fetchTransactions = async (filters: TransactionFilters = {}) => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await adminTransactionsAPI.getAllTransactions({
        page: currentPage,
        limit: 50,
        search: searchQuery || undefined,
        type: activeTab === 'all' ? undefined : activeTab,
        ...filters
      })

      setTransactions(response.transactions)
      setTotalPages(response.pages)

      // Calculate statistics
      const calculatedStats = await adminTransactionsAPI.getTransactionStatistics(response.transactions)
      setStats(calculatedStats)

    } catch (err: any) {
      console.error('Error fetching transactions:', err)
      setError(err.message || 'Failed to load transactions')
      toast.error('Failed to load transactions')
    } finally {
      setIsLoading(false)
    }
  }

  // Load data on component mount and when filters change
  useEffect(() => {
    fetchTransactions()
  }, [currentPage, activeTab])

  // Handle search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (currentPage === 1) {
        fetchTransactions()
      } else {
        setCurrentPage(1) // This will trigger fetchTransactions via the useEffect above
      }
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  // Handle transaction status update
  const handleStatusUpdate = async (transactionId: string, newStatus: string, failureReason?: string) => {
    try {
      await adminTransactionsAPI.updateTransactionStatus(transactionId, newStatus, undefined, failureReason)
      toast.success(`Transaction ${newStatus} successfully`)
      fetchTransactions() // Refresh data
    } catch (err: any) {
      console.error('Error updating transaction status:', err)
      toast.error(err.message || 'Failed to update transaction status')
    }
  }

  // Handle export
  const handleExport = async () => {
    try {
      setIsExporting(true)
      const blob = await adminTransactionsAPI.exportTransactions({
        search: searchQuery || undefined,
        type: activeTab === 'all' ? undefined : activeTab
      })

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `transactions-${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success('Transactions exported successfully')
    } catch (err: any) {
      console.error('Error exporting transactions:', err)
      toast.error('Failed to export transactions')
    } finally {
      setIsExporting(false)
    }
  }

  // Calculate fallback stats
  function calculateFallbackStats(): TransactionStats {
    return {
      totalRepayments: 2250,
      totalDisbursements: 4000,
      pendingAmount: 750,
      totalTransactions: 7,
      completedTransactions: 5,
      failedTransactions: 1
    }
  }

  // Fallback transactions data for when API is unavailable
  const fallbackTransactions: Transaction[] = [
    {
      id: "PAY-123456",
      userId: "USR-001",
      userName: "John Doe",
      userPhoneNumber: "+268 7612 3456",
      loanId: "LOAN-123456",
      amount: 500,
      type: "repayment",
      method: "mobile_money",
      status: "completed",
      date: "2025-03-15T14:30:00",
    },
    {
      id: "PAY-789012",
      userId: "USR-002",
      userName: "Sarah Smith",
      loanId: "LOAN-789012",
      amount: 1000,
      type: "disbursement",
      method: "bank_transfer",
      status: "completed",
      date: "2025-03-15T12:15:00",
    },
    {
      id: "PAY-345678",
      userId: "USR-003",
      userName: "Michael Johnson",
      loanId: "LOAN-345678",
      amount: 750,
      type: "repayment",
      method: "mobile_money",
      status: "pending",
      date: "2025-03-15T10:45:00",
    },
    {
      id: "PAY-901234",
      userId: "USR-004",
      userName: "Emily Brown",
      loanId: "LOAN-901234",
      amount: 3000,
      type: "disbursement",
      method: "mobile_money",
      status: "completed",
      date: "2025-03-15T09:20:00",
    },
    {
      id: "PAY-567890",
      userId: "USR-005",
      userName: "David Wilson",
      loanId: "LOAN-567890",
      amount: 1250,
      type: "repayment",
      method: "cash",
      status: "completed",
      date: "2025-03-14T16:50:00",
    },
    {
      id: "PAY-234567",
      userId: "USR-001",
      userName: "John Doe",
      loanId: "LOAN-123456",
      amount: 500,
      type: "repayment",
      method: "mobile_money",
      status: "failed",
      date: "2025-03-14T11:30:00",
    },
    {
      id: "PAY-890123",
      userId: "USR-002",
      userName: "Sarah Smith",
      loanId: "LOAN-789012",
      amount: 1000,
      type: "disbursement",
      method: "bank_transfer",
      status: "pending",
      date: "2025-03-13T15:45:00",
    },
  ]

  // Use real data or fallback
  const displayTransactions = error ? fallbackTransactions : transactions
  const displayStats = error ? calculateFallbackStats() : stats

  // Filter transactions based on search and active tab (client-side filtering for fallback data)
  const filteredTransactions = displayTransactions.filter((transaction) => {
    // Search filter
    const searchMatch = !searchQuery ||
      transaction.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      transaction.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      transaction.userId.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (transaction.loanId && transaction.loanId.toLowerCase().includes(searchQuery.toLowerCase()))

    // Tab filter (only apply if using fallback data, otherwise filtering is done server-side)
    if (error) {
      if (activeTab === "all") return searchMatch
      if (activeTab === "repayments") return searchMatch && transaction.type === "repayment"
      if (activeTab === "disbursements") return searchMatch && transaction.type === "disbursement"
      if (activeTab === "pending") return searchMatch && transaction.status === "pending"
      if (activeTab === "failed") return searchMatch && transaction.status === "failed"
    }

    return searchMatch
  })



  // Get statistics from displayStats or calculate from displayTransactions
  const totalRepayments = displayStats?.totalRepayments || 0
  const totalDisbursements = displayStats?.totalDisbursements || 0
  const pendingAmount = displayStats?.pendingAmount || 0
  const completedTransactions = displayStats?.completedTransactions || 0
  const failedTransactions = displayStats?.failedTransactions || 0

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Payments</h1>
        <Button
          className="bg-blue-600 hover:bg-blue-700"
          onClick={handleExport}
          disabled={isExporting}
        >
          {isExporting ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Download className="mr-2 h-4 w-4" />
          )}
          {isExporting ? 'Exporting...' : 'Export Report'}
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Repayments</CardTitle>
            <CardDescription>Completed payments received</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <ArrowUpRight className="h-4 w-4 text-green-600" />
              <div className="text-2xl font-bold">E{totalRepayments.toLocaleString()}</div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              From {displayTransactions.filter((t) => t.type === "repayment" && t.status === "completed").length} transactions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Disbursements</CardTitle>
            <CardDescription>Completed loan payouts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <ArrowDownRight className="h-4 w-4 text-red-600" />
              <div className="text-2xl font-bold">E{totalDisbursements.toLocaleString()}</div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              From {displayTransactions.filter((t) => t.type === "disbursement" && t.status === "completed").length} transactions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Pending Transactions</CardTitle>
            <CardDescription>Awaiting completion</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div className="text-2xl font-bold">E{pendingAmount.toLocaleString()}</div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              From {displayTransactions.filter((t) => t.status === "pending").length} transactions
            </p>
          </CardContent>
        </Card>
      </div>

      <PaymentChart />

      <Card>
        <CardHeader>
          <CardTitle>Payment Transactions</CardTitle>
          <CardDescription>View and manage all payment transactions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
              <TabsList className="grid grid-cols-5 w-full md:w-auto">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="repayments">Repayments</TabsTrigger>
                <TabsTrigger value="disbursements">Disbursements</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="failed">Failed</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="flex gap-4 w-full md:w-auto">
              <div className="relative w-full md:w-64">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search payments..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>
          </div>

          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Transaction ID</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Loan ID</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-6">
                      <div className="flex items-center justify-center">
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Loading transactions...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredTransactions.length > 0 ? (
                  filteredTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="font-medium">{transaction.id}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{transaction.userName}</span>
                          <span className="text-xs text-muted-foreground">{transaction.userId}</span>
                        </div>
                      </TableCell>
                      <TableCell>{transaction.loanId || 'N/A'}</TableCell>
                      <TableCell>{adminTransactionsAPI.formatCurrency(transaction.amount)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {transaction.type === "repayment" ? (
                            <ArrowUpRight className="h-4 w-4 text-green-600 mr-1" />
                          ) : (
                            <ArrowDownRight className="h-4 w-4 text-red-600 mr-1" />
                          )}
                          {adminTransactionsAPI.getTypeDisplayName(transaction.type)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className={adminTransactionsAPI.getMethodColorClass(transaction.method)}>
                          {adminTransactionsAPI.getMethodDisplayName(transaction.method)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={adminTransactionsAPI.getStatusColorClass(transaction.status)}>
                          {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>{new Date(transaction.date).toLocaleString()}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>View Details</DropdownMenuItem>
                            {transaction.status === "pending" && (
                              <>
                                <DropdownMenuItem
                                  className="text-green-600"
                                  onClick={() => handleStatusUpdate(transaction.id, 'completed')}
                                >
                                  Mark as Completed
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={() => handleStatusUpdate(transaction.id, 'failed', 'Manual rejection')}
                                >
                                  Mark as Failed
                                </DropdownMenuItem>
                              </>
                            )}
                            {transaction.status === "failed" && (
                              <DropdownMenuItem
                                className="text-blue-600"
                                onClick={() => handleStatusUpdate(transaction.id, 'pending')}
                              >
                                Retry Transaction
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>Download Receipt</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-6 text-muted-foreground">
                      No payments found matching your search criteria
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

